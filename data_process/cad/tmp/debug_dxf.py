#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试DXF文件内容，查看实际的图层和实体类型
"""

import ezdxf
import os
from collections import defaultdict

def debug_dxf_file(dxf_path: str):
    """调试DXF文件内容"""
    print(f"🔍 调试DXF文件: {os.path.basename(dxf_path)}")
    print("=" * 60)
    
    try:
        doc = ezdxf.readfile(dxf_path)
        print(f"✅ 成功加载DXF文件")
        print(f"📋 DXF版本: {doc.dxfversion}")
    except Exception as e:
        print(f"❌ 加载DXF文件失败: {e}")
        return
    
    # 统计信息
    layer_stats = defaultdict(lambda: defaultdict(int))
    entity_stats = defaultdict(int)
    text_entities = []
    
    print(f"\n📊 实体统计:")
    print("-" * 40)
    
    # 遍历模型空间
    for entity in doc.modelspace():
        entity_type = entity.dxftype()
        layer = getattr(entity.dxf, 'layer', '0')
        
        entity_stats[entity_type] += 1
        layer_stats[layer][entity_type] += 1
        
        # 收集文本实体
        if entity_type in ['TEXT', 'MTEXT']:
            try:
                if entity_type == 'TEXT':
                    text_content = entity.dxf.text
                else:
                    text_content = entity.plain_text()
                
                if text_content.strip():
                    text_entities.append({
                        'type': entity_type,
                        'layer': layer,
                        'text': text_content.strip(),
                        'position': (entity.dxf.insert.x, entity.dxf.insert.y) if hasattr(entity.dxf, 'insert') else None
                    })
            except:
                pass
    
    # 显示实体类型统计
    print("实体类型统计:")
    for entity_type, count in sorted(entity_stats.items()):
        print(f"  {entity_type}: {count}")
    
    print(f"\n📋 图层统计 (共{len(layer_stats)}个图层):")
    print("-" * 40)
    
    # 显示图层统计
    for layer, entities in sorted(layer_stats.items()):
        total = sum(entities.values())
        print(f"图层 '{layer}' (共{total}个实体):")
        for entity_type, count in sorted(entities.items()):
            print(f"  - {entity_type}: {count}")
        print()
    
    print(f"\n📝 文本内容样例 (共{len(text_entities)}个文本实体):")
    print("-" * 40)
    
    # 显示文本内容样例
    for i, text_info in enumerate(text_entities[:20]):  # 只显示前20个
        print(f"{i+1:2d}. [{text_info['layer']}] {text_info['type']}: {text_info['text'][:50]}{'...' if len(text_info['text']) > 50 else ''}")
    
    if len(text_entities) > 20:
        print(f"... 还有 {len(text_entities) - 20} 个文本实体")
    
    # 检查特定图层
    print(f"\n🔍 检查特定图层:")
    print("-" * 40)
    
    target_layers = ['TK_BTXT', 'TK_BTWZ', 'SYST']
    for layer in target_layers:
        if layer in layer_stats:
            entities = layer_stats[layer]
            total = sum(entities.values())
            print(f"✅ 图层 '{layer}' 存在 (共{total}个实体):")
            for entity_type, count in entities.items():
                print(f"    - {entity_type}: {count}")
        else:
            print(f"❌ 图层 '{layer}' 不存在")
    
    # 检查所有图层名称
    print(f"\n📋 所有图层名称:")
    print("-" * 40)
    all_layers = sorted(layer_stats.keys())
    for i, layer in enumerate(all_layers):
        print(f"{i+1:2d}. '{layer}'")
    
    return {
        'entity_stats': dict(entity_stats),
        'layer_stats': dict(layer_stats),
        'text_entities': text_entities,
        'total_entities': sum(entity_stats.values()),
        'total_layers': len(layer_stats)
    }

def main():
    """主函数"""
    # 使用测试文件
    dxf_path = '/Users/<USER>/work/移动/项目-农商文旅/00-00 大数据/2025 市场项目/中广核/图纸格式转化_test/BJ0EEX96101DETX43DD11CCFC0BEE火灾自动报警系统配线图10.dxf'
    
    if not os.path.exists(dxf_path):
        print(f"❌ 文件不存在: {dxf_path}")
        return
    
    debug_info = debug_dxf_file(dxf_path)
    
    if debug_info:
        print(f"\n📊 总结:")
        print(f"  总实体数: {debug_info['total_entities']}")
        print(f"  总图层数: {debug_info['total_layers']}")
        print(f"  文本实体数: {len(debug_info['text_entities'])}")

if __name__ == '__main__':
    main()
