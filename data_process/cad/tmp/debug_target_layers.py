#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试目标图层的坐标分布
"""

import ezdxf
import os
from collections import defaultdict

def debug_target_layers(dxf_path: str):
    """调试目标图层的坐标分布"""
    print(f"🔍 调试目标图层坐标分布: {os.path.basename(dxf_path)}")
    print("=" * 80)
    
    try:
        doc = ezdxf.readfile(dxf_path)
        print(f"✅ 成功加载DXF文件")
    except Exception as e:
        print(f"❌ 加载DXF文件失败: {e}")
        return
    
    target_layers = ['TK_BTXT', 'TK_BTWZ', 'SYST']
    
    # 收集所有实体
    all_entities = []
    
    # 1. 模型空间
    for entity in doc.modelspace():
        all_entities.append(('模型空间', entity))
    
    # 2. 图纸空间
    for layout in doc.layouts:
        if layout.name != 'Model':
            for entity in layout:
                all_entities.append((f'图纸空间-{layout.name}', entity))

    # 3. 块定义
    for block in doc.blocks:
        if not block.name.startswith('*'):
            for entity in block:
                all_entities.append((f'块-{block.name}', entity))
    
    print(f"总实体数: {len(all_entities)}")
    
    # 分析每个目标图层
    for target_layer in target_layers:
        print(f"\n📊 分析图层: {target_layer}")
        print("-" * 50)
        
        layer_entities = []
        space_stats = defaultdict(int)
        type_stats = defaultdict(int)
        
        for space_name, entity in all_entities:
            if entity.dxf.layer.upper() == target_layer:
                layer_entities.append((space_name, entity))
                space_stats[space_name] += 1
                type_stats[entity.dxftype()] += 1
        
        print(f"总数: {len(layer_entities)}")
        
        if not layer_entities:
            print("❌ 未找到该图层的实体")
            continue
        
        # 显示空间分布
        print("空间分布:")
        for space, count in sorted(space_stats.items()):
            print(f"  {space}: {count}")
        
        # 显示实体类型分布
        print("实体类型分布:")
        for entity_type, count in sorted(type_stats.items()):
            print(f"  {entity_type}: {count}")
        
        # 分析坐标分布
        coordinates = []
        for space_name, entity in layer_entities:
            pos = get_entity_position(entity)
            if pos:
                coordinates.append(pos)
        
        if coordinates:
            x_coords = [pos[0] for pos in coordinates]
            y_coords = [pos[1] for pos in coordinates]
            
            x_min, x_max = min(x_coords), max(x_coords)
            y_min, y_max = min(y_coords), max(y_coords)
            
            print(f"坐标范围:")
            print(f"  X: {x_min:.2f} ~ {x_max:.2f} (范围: {x_max - x_min:.2f})")
            print(f"  Y: {y_min:.2f} ~ {y_max:.2f} (范围: {y_max - y_min:.2f})")
            
            # 显示一些样例坐标
            print("样例坐标 (前10个):")
            for i, (pos, (space_name, entity)) in enumerate(zip(coordinates[:10], layer_entities[:10])):
                print(f"  {i+1}. ({pos[0]:.2f}, {pos[1]:.2f}) - {entity.dxftype()} in {space_name}")
        else:
            print("❌ 无法获取坐标信息")

def get_entity_position(entity):
    """获取实体位置"""
    try:
        if hasattr(entity.dxf, 'insert'):
            return (entity.dxf.insert.x, entity.dxf.insert.y)
        elif hasattr(entity.dxf, 'start'):
            return (entity.dxf.start.x, entity.dxf.start.y)
        elif hasattr(entity.dxf, 'center'):
            return (entity.dxf.center.x, entity.dxf.center.y)
    except:
        pass
    return None

def main():
    """主函数"""
    # 使用测试文件
    dxf_path = '/Users/<USER>/work/移动/项目-农商文旅/00-00 大数据/2025 市场项目/中广核/图纸格式转化_test/BJ0EEX96101DETX43DD11CCFC0BEE火灾自动报警系统配线图10.dxf'
    
    if not os.path.exists(dxf_path):
        print(f"❌ 文件不存在: {dxf_path}")
        return
    
    debug_target_layers(dxf_path)

if __name__ == '__main__':
    main()
