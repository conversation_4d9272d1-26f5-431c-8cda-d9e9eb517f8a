#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
完整调试DXF文件内容，检查所有空间（模型空间、图纸空间、块定义）
"""

import ezdxf
import os
from collections import defaultdict

def debug_dxf_complete(dxf_path: str):
    """完整调试DXF文件内容"""
    print(f"🔍 完整调试DXF文件: {os.path.basename(dxf_path)}")
    print("=" * 80)
    
    try:
        doc = ezdxf.readfile(dxf_path)
        print(f"✅ 成功加载DXF文件")
        print(f"📋 DXF版本: {doc.dxfversion}")
    except Exception as e:
        print(f"❌ 加载DXF文件失败: {e}")
        return
    
    all_layers = set()
    space_stats = {}
    
    # 1. 检查模型空间
    print(f"\n📊 1. 模型空间 (ModelSpace):")
    print("-" * 50)
    
    model_layer_stats = defaultdict(lambda: defaultdict(int))
    model_entity_count = 0
    
    for entity in doc.modelspace():
        entity_type = entity.dxftype()
        layer = getattr(entity.dxf, 'layer', '0')
        
        model_layer_stats[layer][entity_type] += 1
        all_layers.add(layer)
        model_entity_count += 1
    
    space_stats['模型空间'] = {
        'entity_count': model_entity_count,
        'layer_count': len(model_layer_stats),
        'layers': dict(model_layer_stats)
    }
    
    print(f"实体总数: {model_entity_count}")
    print(f"图层数量: {len(model_layer_stats)}")
    
    # 显示前10个图层
    for i, (layer, entities) in enumerate(sorted(model_layer_stats.items())):
        if i >= 10:
            print(f"... 还有 {len(model_layer_stats) - 10} 个图层")
            break
        total = sum(entities.values())
        print(f"  '{layer}': {total} 个实体")
    
    # 2. 检查图纸空间
    print(f"\n📊 2. 图纸空间 (Paper Space):")
    print("-" * 50)
    
    paper_total_entities = 0
    paper_total_layers = set()
    
    for layout in doc.layouts:
        if layout.name != 'Model':  # 跳过模型空间
            print(f"\n  布局: '{layout.name}'")
            layout_layer_stats = defaultdict(lambda: defaultdict(int))
            layout_entity_count = 0
            
            for entity in layout:
                entity_type = entity.dxftype()
                layer = getattr(entity.dxf, 'layer', '0')
                
                layout_layer_stats[layer][entity_type] += 1
                all_layers.add(layer)
                paper_total_layers.add(layer)
                layout_entity_count += 1
                paper_total_entities += 1
            
            print(f"    实体数: {layout_entity_count}")
            print(f"    图层数: {len(layout_layer_stats)}")
            
            # 显示该布局的图层
            for layer, entities in sorted(layout_layer_stats.items()):
                total = sum(entities.values())
                print(f"      '{layer}': {total} 个实体")
            
            space_stats[f'图纸空间-{layout.name}'] = {
                'entity_count': layout_entity_count,
                'layer_count': len(layout_layer_stats),
                'layers': dict(layout_layer_stats)
            }
    
    print(f"\n图纸空间总计: {paper_total_entities} 个实体, {len(paper_total_layers)} 个图层")
    
    # 3. 检查块定义
    print(f"\n📊 3. 块定义 (Block Definitions):")
    print("-" * 50)
    
    block_total_entities = 0
    block_total_layers = set()
    
    for block in doc.blocks:
        if not block.name.startswith('*'):  # 跳过匿名块
            block_layer_stats = defaultdict(lambda: defaultdict(int))
            block_entity_count = 0
            
            for entity in block:
                entity_type = entity.dxftype()
                layer = getattr(entity.dxf, 'layer', '0')
                
                block_layer_stats[layer][entity_type] += 1
                all_layers.add(layer)
                block_total_layers.add(layer)
                block_entity_count += 1
                block_total_entities += 1
            
            if block_entity_count > 0:  # 只显示非空块
                print(f"\n  块: '{block.name}'")
                print(f"    实体数: {block_entity_count}")
                print(f"    图层数: {len(block_layer_stats)}")
                
                # 显示该块的图层
                for layer, entities in sorted(block_layer_stats.items()):
                    total = sum(entities.values())
                    print(f"      '{layer}': {total} 个实体")
    
    print(f"\n块定义总计: {block_total_entities} 个实体, {len(block_total_layers)} 个图层")
    
    # 4. 检查目标图层
    print(f"\n🎯 4. 检查目标图层:")
    print("-" * 50)
    
    target_layers = ['TK_BTXT', 'TK_BTWZ', 'SYST']
    
    for target_layer in target_layers:
        found_spaces = []
        total_entities = 0
        
        # 在所有空间中查找
        for space_name, space_info in space_stats.items():
            if target_layer in space_info['layers']:
                layer_entities = space_info['layers'][target_layer]
                entity_count = sum(layer_entities.values())
                found_spaces.append(f"{space_name}({entity_count}个实体)")
                total_entities += entity_count
        
        if found_spaces:
            print(f"✅ 图层 '{target_layer}' 找到:")
            for space in found_spaces:
                print(f"    - {space}")
            print(f"    总计: {total_entities} 个实体")
        else:
            print(f"❌ 图层 '{target_layer}' 未找到")
    
    # 5. 显示所有图层名称（按字母排序）
    print(f"\n📋 5. 所有图层名称 (共{len(all_layers)}个):")
    print("-" * 50)
    
    sorted_layers = sorted(all_layers)
    for i, layer in enumerate(sorted_layers):
        print(f"{i+1:3d}. '{layer}'")
    
    # 6. 查找可能的相似图层名
    print(f"\n🔍 6. 查找相似图层名:")
    print("-" * 50)
    
    for target in target_layers:
        similar_layers = []
        target_upper = target.upper()
        target_lower = target.lower()
        
        for layer in sorted_layers:
            layer_upper = layer.upper()
            layer_lower = layer.lower()
            
            # 检查包含关系
            if (target_upper in layer_upper or 
                target_lower in layer_lower or
                any(part in layer_upper for part in target_upper.split('_')) or
                any(part in layer_lower for part in target_lower.split('_'))):
                similar_layers.append(layer)
        
        if similar_layers:
            print(f"与 '{target}' 相似的图层:")
            for layer in similar_layers:
                print(f"  - '{layer}'")
        else:
            print(f"未找到与 '{target}' 相似的图层")
    
    return {
        'all_layers': sorted_layers,
        'space_stats': space_stats,
        'total_entities': sum(info['entity_count'] for info in space_stats.values()),
        'total_layers': len(all_layers)
    }

def main():
    """主函数"""
    # 使用测试文件
    dxf_path = '/Users/<USER>/work/移动/项目-农商文旅/00-00 大数据/2025 市场项目/中广核/图纸格式转化_test/BJ0EEX96101DETX43DD11CCFC0BEE火灾自动报警系统配线图10.dxf'
    
    if not os.path.exists(dxf_path):
        print(f"❌ 文件不存在: {dxf_path}")
        return
    
    debug_info = debug_dxf_complete(dxf_path)
    
    if debug_info:
        print(f"\n📊 最终总结:")
        print(f"  总实体数: {debug_info['total_entities']}")
        print(f"  总图层数: {debug_info['total_layers']}")

if __name__ == '__main__':
    main()
