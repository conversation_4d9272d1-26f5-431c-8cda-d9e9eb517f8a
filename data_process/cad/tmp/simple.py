import ezdxf
import json
import os
import logging
from pathlib import Path
from collections import defaultdict
from typing import Dict, List, Any, Tuple, Optional
import numpy as np

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def safe_coordinate_conversion(coord) -> List[float]:
    """安全地将坐标对象转换为列表"""
    if coord is None:
        return []
    try:
        if isinstance(coord, (list, tuple)):
            return [float(x) for x in coord]
        if hasattr(coord, 'x') and hasattr(coord, 'y'):
            z = getattr(coord, 'z', 0.0)
            return [float(coord.x), float(coord.y), float(z)]
        if hasattr(coord, '__iter__'):
            return [float(x) for x in coord]
        return [float(coord)]
    except Exception as e:
        logger.warning(f"坐标转换失败: {e}")
        return []


class DXFComprehensiveParser:
    """DXF全面解析器"""

    def __init__(self, dxf_path: str):
        self.dxf_path = dxf_path
        self.doc = None
        self.all_entities = []
        self.layer_entities = defaultdict(list)

    def load_document(self) -> bool:
        """加载DXF文档"""
        try:
            self.doc = ezdxf.readfile(self.dxf_path)
            logger.info(f"成功加载DXF文件: {self.dxf_path}")
            return True
        except Exception as e:
            logger.error(f"无法读取DXF文件 {self.dxf_path}: {e}")
            return False

    def collect_all_entities(self):
        """收集所有实体并按图层分类"""
        logger.info("开始收集所有实体...")

        # 收集模型空间实体
        for entity in self.doc.modelspace():
            entity_info = self._extract_comprehensive_entity_info(entity, "模型空间")
            if entity_info:
                layer = entity_info["所在图层"]
                self.layer_entities[layer].append(entity_info)
                self.all_entities.append(entity_info)

        # 收集图纸空间实体
        for layout in self.doc.layouts:
            if layout.name != 'Model':
                for entity in layout:
                    entity_info = self._extract_comprehensive_entity_info(entity, f"图纸空间-{layout.name}")
                    if entity_info:
                        layer = entity_info["所在图层"]
                        self.layer_entities[layer].append(entity_info)
                        self.all_entities.append(entity_info)

        # 收集块定义实体
        for block in self.doc.blocks:
            if not block.name.startswith('*'):
                for entity in block:
                    entity_info = self._extract_comprehensive_entity_info(entity, f"块定义-{block.name}")
                    if entity_info:
                        layer = entity_info["所在图层"]
                        self.layer_entities[layer].append(entity_info)
                        self.all_entities.append(entity_info)

        logger.info(f"收集完成 - 总实体数: {len(self.all_entities)}, 图层数: {len(self.layer_entities)}")

        # 记录图层统计
        for layer, entities in self.layer_entities.items():
            logger.info(f"图层 {layer}: {len(entities)} 个实体")

    def _extract_comprehensive_entity_info(self, entity, space_name: str) -> Dict:
        """提取完整的实体信息"""
        entity_type = entity.dxftype()

        entity_info = {
            "实体类型": entity_type,
            "所在图层": getattr(entity.dxf, 'layer', '0'),
            "颜色索引": getattr(entity.dxf, 'color', 0),
            "所在空间": space_name
        }

        # 提取坐标和内容信息
        if entity_type in ['TEXT', 'MTEXT']:
            entity_info["坐标"] = safe_coordinate_conversion(entity.dxf.insert)
            if entity_type == 'TEXT':
                entity_info["文本内容"] = getattr(entity.dxf, 'text', '')
                entity_info["文本高度"] = getattr(entity.dxf, 'height', 0)
            else:  # MTEXT
                entity_info["文本内容"] = entity.plain_text()
                entity_info["文本高度"] = getattr(entity.dxf, 'char_height', 0)
            entity_info["旋转角度"] = getattr(entity.dxf, 'rotation', 0)

        elif entity_type == 'INSERT':
            entity_info["坐标"] = safe_coordinate_conversion(entity.dxf.insert)
            entity_info["块名称"] = getattr(entity.dxf, 'name', '')
            entity_info["缩放比例"] = [
                getattr(entity.dxf, 'xscale', 1.0),
                getattr(entity.dxf, 'yscale', 1.0),
                getattr(entity.dxf, 'zscale', 1.0)
            ]
            # 提取块属性
            if hasattr(entity, 'attribs'):
                attributes = []
                for attrib in entity.attribs:
                    if hasattr(attrib.dxf, 'text') and attrib.dxf.text:
                        attributes.append({
                            "标签": getattr(attrib.dxf, 'tag', ''),
                            "值": attrib.dxf.text,
                            "坐标": safe_coordinate_conversion(attrib.dxf.insert)
                        })
                entity_info["块属性"] = attributes

        elif entity_type == 'LINE':
            entity_info["起点"] = safe_coordinate_conversion(entity.dxf.start)
            entity_info["终点"] = safe_coordinate_conversion(entity.dxf.end)

        elif entity_type in ['LWPOLYLINE', 'POLYLINE']:
            vertices = []
            for vertex in entity:
                if hasattr(vertex, 'dxf'):
                    vertices.append(safe_coordinate_conversion(vertex.dxf.location))
                else:
                    vertices.append(safe_coordinate_conversion(vertex))
            entity_info["顶点"] = vertices
            entity_info["是否闭合"] = entity.is_closed

        elif entity_type == 'CIRCLE':
            entity_info["圆心"] = safe_coordinate_conversion(entity.dxf.center)
            entity_info["半径"] = entity.dxf.radius

        elif entity_type == 'ARC':
            entity_info["圆心"] = safe_coordinate_conversion(entity.dxf.center)
            entity_info["半径"] = entity.dxf.radius
            entity_info["起始角度"] = entity.dxf.start_angle
            entity_info["终止角度"] = entity.dxf.end_angle

        elif entity_type == 'DIMENSION':
            entity_info["标注文本"] = getattr(entity.dxf, 'text', '')
            if hasattr(entity.dxf, 'text_midpoint'):
                entity_info["坐标"] = safe_coordinate_conversion(entity.dxf.text_midpoint)

        return entity_info

    def detect_drawing_sheets(self) -> List[Dict]:
        """检测多个图纸区域"""
        logger.info("开始检测图纸区域...")

        # 收集所有有坐标的实体
        entities_with_coords = []
        for entity in self.all_entities:
            coords = entity.get("坐标")
            if coords and len(coords) >= 2:
                x, y = coords[0], coords[1]
                # 过滤异常坐标
                if abs(x) < 1e7 and abs(y) < 1e7 and not (x == 0 and y == 0):
                    entities_with_coords.append((x, y, entity))

        if not entities_with_coords:
            logger.warning("未找到有效坐标的实体")
            return []

        logger.info(f"找到 {len(entities_with_coords)} 个有坐标的实体")

        # 按X坐标排序
        entities_with_coords.sort(key=lambda x: x[0])
        x_coords = [coord[0] for coord in entities_with_coords]
        y_coords = [coord[1] for coord in entities_with_coords]

        x_min, x_max = min(x_coords), max(x_coords)
        y_min, y_max = min(y_coords), max(y_coords)
        x_range = x_max - x_min

        logger.info(f"坐标范围 - X: [{x_min:.2f}, {x_max:.2f}], Y: [{y_min:.2f}, {y_max:.2f}]")

        # 判断是否为多图纸
        if x_range < 50000:  # 单图纸阈值调整
            logger.info("检测到单图纸")
            return [{
                "图纸名称": "图纸1",
                "X范围": [x_min, x_max],
                "Y范围": [y_min, y_max],
                "实体数量": len(entities_with_coords)
            }]

        # 多图纸检测
        try:
            from sklearn.cluster import KMeans
            coords_array = np.array([(coord[0], coord[1]) for coord in entities_with_coords])

            # 尝试2个聚类
            kmeans = KMeans(n_clusters=2, random_state=42, n_init=10)
            cluster_labels = kmeans.fit_predict(coords_array)

            cluster_0_coords = coords_array[cluster_labels == 0]
            cluster_1_coords = coords_array[cluster_labels == 1]

            center_0_x = np.mean(cluster_0_coords[:, 0])
            center_1_x = np.mean(cluster_1_coords[:, 0])

            # 检查聚类质量
            if (abs(center_1_x - center_0_x) > x_range * 0.2 and
                    len(cluster_0_coords) > 20 and len(cluster_1_coords) > 20):

                logger.info("检测到双图纸")

                # 确定左右图纸
                if center_0_x < center_1_x:
                    left_coords, right_coords = cluster_0_coords, cluster_1_coords
                else:
                    left_coords, right_coords = cluster_1_coords, cluster_0_coords

                return [
                    {
                        "图纸名称": "图纸1",
                        "X范围": [float(np.min(left_coords[:, 0])), float(np.max(left_coords[:, 0]))],
                        "Y范围": [float(np.min(left_coords[:, 1])), float(np.max(left_coords[:, 1]))],
                        "实体数量": len(left_coords)
                    },
                    {
                        "图纸名称": "图纸2",
                        "X范围": [float(np.min(right_coords[:, 0])), float(np.max(right_coords[:, 0]))],
                        "Y范围": [float(np.min(right_coords[:, 1])), float(np.max(right_coords[:, 1]))],
                        "实体数量": len(right_coords)
                    }
                ]

        except ImportError:
            logger.warning("sklearn未安装，使用简单分割方法")

        # 简单分割方法
        x_mid = (x_min + x_max) / 2
        left_entities = [coord for coord in entities_with_coords if coord[0] < x_mid]
        right_entities = [coord for coord in entities_with_coords if coord[0] >= x_mid]

        if len(left_entities) > 20 and len(right_entities) > 20:
            logger.info("使用简单分割检测到双图纸")
            return [
                {
                    "图纸名称": "图纸1",
                    "X范围": [min(coord[0] for coord in left_entities), max(coord[0] for coord in left_entities)],
                    "Y范围": [min(coord[1] for coord in left_entities), max(coord[1] for coord in left_entities)],
                    "实体数量": len(left_entities)
                },
                {
                    "图纸名称": "图纸2",
                    "X范围": [min(coord[0] for coord in right_entities), max(coord[0] for coord in right_entities)],
                    "Y范围": [min(coord[1] for coord in right_entities), max(coord[1] for coord in right_entities)],
                    "实体数量": len(right_entities)
                }
            ]

        # 默认单图纸
        logger.info("默认为单图纸")
        return [{
            "图纸名称": "图纸1",
            "X范围": [x_min, x_max],
            "Y范围": [y_min, y_max],
            "实体数量": len(entities_with_coords)
        }]

    def parse_sheet_content(self, sheet: Dict) -> Dict:
        """解析图纸内容"""
        logger.info(f"开始解析 {sheet['图纸名称']} 的内容...")

        # 筛选属于当前图纸的实体
        sheet_entities = self._filter_entities_by_sheet(sheet)

        logger.info(f"{sheet['图纸名称']} 包含 {len(sheet_entities)} 个实体")

        # 按图层分组
        sheet_layers = defaultdict(list)
        for entity in sheet_entities:
            layer = entity["所在图层"]
            sheet_layers[layer].append(entity)

        # 记录关键图层
        key_layers = ['TK_BTXT', 'SYST', 'TK_BTWZ']
        for layer in key_layers:
            count = len(sheet_layers.get(layer, []))
            if count > 0:
                logger.info(f"{sheet['图纸名称']} - {layer}图层: {count} 个实体")
            else:
                logger.warning(f"{sheet['图纸名称']} - 未找到{layer}图层实体")

        # 解析表格
        tables = self._parse_tables(sheet_entities, sheet_layers)

        # 解析图例
        legends = self._parse_legends(sheet_entities, sheet_layers)

        # 主图内容（排除表格和图例）
        main_content = self._parse_main_content(sheet_entities, sheet_layers)

        return {
            "主图": main_content,
            "表格": tables,
            "图例": legends,
            "图层统计": {layer: len(entities) for layer, entities in sheet_layers.items()}
        }

    def _filter_entities_by_sheet(self, sheet: Dict) -> List[Dict]:
        """筛选属于指定图纸的实体"""
        sheet_entities = []
        x_range = sheet["X范围"]
        y_range = sheet["Y范围"]

        for entity in self.all_entities:
            coords = entity.get("坐标")
            if coords and len(coords) >= 2:
                x, y = coords[0], coords[1]
                if x_range[0] <= x <= x_range[1] and y_range[0] <= y <= y_range[1]:
                    sheet_entities.append(entity)
            else:
                # 对于没有坐标的实体，也包含进来
                sheet_entities.append(entity)

        return sheet_entities

    def _parse_tables(self, sheet_entities: List[Dict], sheet_layers: Dict) -> List[Dict]:
        """解析表格"""
        logger.info("开始解析表格...")

        tables = []

        # 解析SYST图层的图表数据
        syst_entities = sheet_layers.get('SYST', [])
        if syst_entities:
            logger.info(f"解析SYST图层表格，包含 {len(syst_entities)} 个实体")
            syst_table = self._parse_syst_table(syst_entities)
            if syst_table:
                tables.append(syst_table)

        # 解析TK_BTWZ和TK_BTXT组合的表格
        text_entities = sheet_layers.get('TK_BTWZ', [])
        frame_entities = sheet_layers.get('TK_BTXT', [])

        if text_entities and frame_entities:
            logger.info(f"解析TK表格 - 文字: {len(text_entities)}, 线框: {len(frame_entities)}")
            tk_table = self._reconstruct_table_from_text_and_frame(text_entities, frame_entities)
            if tk_table:
                tables.append(tk_table)
        elif text_entities:
            logger.info(f"仅有文字实体，尝试解析为简单表格: {len(text_entities)}")
            simple_table = self._parse_simple_text_table(text_entities)
            if simple_table:
                tables.append(simple_table)

        logger.info(f"共解析出 {len(tables)} 个表格")
        return tables

    def _parse_syst_table(self, syst_entities: List[Dict]) -> Optional[Dict]:
        """解析SYST图层的表格数据"""
        text_entities = [e for e in syst_entities if e.get("文本内容")]
        if not text_entities:
            return None

        # 按坐标组织文本
        chart_data = []
        for entity in text_entities:
            coords = entity.get("坐标", [])
            if len(coords) >= 2:
                chart_data.append({
                    "x": coords[0],
                    "y": coords[1],
                    "text": entity["文本内容"].strip()
                })

        if not chart_data:
            return None

        # 按Y坐标分组（行）
        chart_data.sort(key=lambda x: -x["y"])  # 从上到下

        rows = []
        current_row = []
        current_y = None
        tolerance = 50  # 行间距容差

        for item in chart_data:
            if current_y is None or abs(item["y"] - current_y) > tolerance:
                if current_row:
                    current_row.sort(key=lambda x: x["x"])  # 按X坐标排序
                    rows.append([t["text"] for t in current_row])
                current_row = [item]
                current_y = item["y"]
            else:
                current_row.append(item)

        if current_row:
            current_row.sort(key=lambda x: x["x"])
            rows.append([t["text"] for t in current_row])

        return {
            "表格名称": "表格1",
            "表格类型": "SYST图表数据",
            "行数": len(rows),
            "列数": max(len(row) for row in rows) if rows else 0,
            "表格数据": rows,
            "原始实体数": len(syst_entities),
            "原始实体": syst_entities
        }

    def _reconstruct_table_from_text_and_frame(self, text_entities: List[Dict], frame_entities: List[Dict]) -> Optional[
        Dict]:
        """从文字和线框重建表格"""
        # 提取所有线段
        lines = []
        for entity in frame_entities:
            if entity["实体类型"] == "LINE":
                start = entity.get("起点", [])
                end = entity.get("终点", [])
                if len(start) >= 2 and len(end) >= 2:
                    lines.append({"start": start, "end": end})
            elif entity["实体类型"] in ["LWPOLYLINE", "POLYLINE"]:
                vertices = entity.get("顶点", [])
                for i in range(len(vertices) - 1):
                    if len(vertices[i]) >= 2 and len(vertices[i + 1]) >= 2:
                        lines.append({"start": vertices[i], "end": vertices[i + 1]})

        if not lines:
            logger.warning("未找到有效的线框数据")
            return None

        logger.info(f"提取到 {len(lines)} 条线段")

        # 构建表格单元格
        cells = self._build_table_cells_from_lines(lines)
        if not cells:
            logger.warning("无法构建表格单元格")
            return None

        logger.info(f"构建了 {len(cells)} 个单元格")

        # 将文本分配到单元格
        self._assign_texts_to_cells(text_entities, cells)

        # 构建表格数据
        table_data = self._build_table_structure_from_cells(cells)

        return {
            "表格名称": "表格2",
            "表格类型": "TK文字线框表格",
            "行数": len(table_data),
            "列数": max(len(row) for row in table_data) if table_data else 0,
            "表格数据": table_data,
            "单元格数": len(cells),
            "frame_entities": frame_entities
        }

    def _build_table_cells_from_lines(self, lines: List[Dict]) -> List[Dict]:
        """从线段构建表格单元格"""
        x_coords = set()
        y_coords = set()

        # 收集垂直线和水平线的坐标
        for line in lines:
            start = line["start"]
            end = line["end"]

            # 垂直线（X坐标相近）
            if abs(start[0] - end[0]) < 10:
                x_coords.add(start[0])
            # 水平线（Y坐标相近）
            if abs(start[1] - end[1]) < 10:
                y_coords.add(start[1])

        if len(x_coords) < 2 or len(y_coords) < 2:
            logger.warning(f"网格线不足 - X线: {len(x_coords)}, Y线: {len(y_coords)}")
            return []

        sorted_x = sorted(list(x_coords))
        sorted_y = sorted(list(y_coords))

        logger.info(f"网格 - X: {len(sorted_x)} 条, Y: {len(sorted_y)} 条")

        # 生成单元格
        cells = []
        for i in range(len(sorted_x) - 1):
            for j in range(len(sorted_y) - 1):
                cell = {
                    "bbox": (sorted_x[i], sorted_y[j], sorted_x[i + 1], sorted_y[j + 1]),
                    "row": j,
                    "col": i,
                    "content": []
                }
                cells.append(cell)

        return cells

    def _assign_texts_to_cells(self, text_entities: List[Dict], cells: List[Dict]):
        """将文本分配到对应的单元格"""
        assigned_count = 0
        for text in text_entities:
            coords = text.get("坐标")
            if not coords or len(coords) < 2:
                continue

            text_x, text_y = coords[0], coords[1]
            text_content = text.get("文本内容", "").strip()

            if not text_content:
                continue

            for cell in cells:
                x_min, y_min, x_max, y_max = cell["bbox"]
                if x_min < text_x < x_max and y_min < text_y < y_max:
                    cell["content"].append(text_content)
                    assigned_count += 1
                    break

        logger.info(f"成功分配 {assigned_count} 个文本到单元格")

    def _build_table_structure_from_cells(self, cells: List[Dict]) -> List[List[str]]:
        """从单元格构建表格结构"""
        if not cells:
            return []

        max_row = max(cell["row"] for cell in cells)
        max_col = max(cell["col"] for cell in cells)

        table_data = []
        for row in range(max_row + 1):
            row_data = []
            for col in range(max_col + 1):
                cell_content = ""
                for cell in cells:
                    if cell["row"] == row and cell["col"] == col:
                        cell_content = " ".join(cell["content"])
                        break
                row_data.append(cell_content)
            table_data.append(row_data)

        return table_data

    def _parse_simple_text_table(self, text_entities: List[Dict]) -> Optional[Dict]:
        """解析简单文本表格"""
        text_data = []
        for entity in text_entities:
            coords = entity.get("坐标")
            content = entity.get("文本内容", "").strip()
            if coords and len(coords) >= 2 and content:
                text_data.append({
                    "x": coords[0],
                    "y": coords[1],
                    "text": content
                })

        if not text_data:
            return None

        # 按Y坐标分组
        text_data.sort(key=lambda x: -x["y"])
        rows = []
        current_row = []
        current_y = None

        for item in text_data:
            if current_y is None or abs(item["y"] - current_y) > 100:
                if current_row:
                    current_row.sort(key=lambda x: x["x"])
                    rows.append([t["text"] for t in current_row])
                current_row = [item]
                current_y = item["y"]
            else:
                current_row.append(item)

        if current_row:
            current_row.sort(key=lambda x: x["x"])
            rows.append([t["text"] for t in current_row])

        return {
            "表格名称": "表格3",
            "表格类型": "简单文本表格",
            "行数": len(rows),
            "列数": max(len(row) for row in rows) if rows else 0,
            "表格数据": rows
        }

    def _parse_legends(self, sheet_entities: List[Dict], sheet_layers: Dict) -> List[Dict]:
        """解析图例"""
        legends = []

        # 查找包含图例关键词的实体
        legend_keywords = ["图例", "说明", "符号", "标识", "LEGEND"]

        for layer, entities in sheet_layers.items():
            legend_texts = []
            for entity in entities:
                text_content = entity.get("文本内容", "")
                if text_content and any(keyword in text_content for keyword in legend_keywords):
                    legend_texts.append(text_content)

            if legend_texts:
                legends.append({
                    "图例名称": f"图例1",
                    "图层": layer,
                    "内容": legend_texts
                })

        return legends

    def _parse_main_content(self, sheet_entities: List[Dict], sheet_layers: Dict) -> Dict:
        """解析主图内容"""
        # 排除表格和图例相关的图层
        exclude_layers = {'TK_BTXT', 'TK_BTWZ', 'SYST'}

        main_layers = {}
        total_entities = 0

        for layer, entities in sheet_layers.items():
            if layer not in exclude_layers:
                main_layers[layer] = len(entities)
                total_entities += len(entities)

        return {
            "图层统计": main_layers,
            "总实体数": total_entities,
            "主要图层": sorted(main_layers.items(), key=lambda x: x[1], reverse=True)[:5]
        }

    def generate_output(self) -> Dict:
        """生成最终输出"""
        if not self.load_document():
            return {"错误": "无法加载DXF文件"}

        # 收集所有实体
        self.collect_all_entities()

        # 检测图纸区域
        drawing_sheets = self.detect_drawing_sheets()

        # 解析每个图纸的内容
        result = {
            "文件名": os.path.basename(self.dxf_path),
            "DXF版本": self.doc.dxfversion,
            "总图层数": len(self.layer_entities),
            "总实体数": len(self.all_entities),
            "图纸数量": len(drawing_sheets)
        }

        for sheet in drawing_sheets:
            sheet_content = self.parse_sheet_content(sheet)
            result[sheet["图纸名称"]] = sheet_content

        return result


def process_dxf_files(input_path: str, output_dir: str = None):
    """处理DXF文件"""
    input_path = Path(input_path)

    if not input_path.exists():
        raise FileNotFoundError(f"输入路径不存在: {input_path}")

    # 获取DXF文件列表
    if input_path.is_file():
        dxf_files = [input_path] if input_path.suffix.lower() == '.dxf' else []
    else:
        dxf_files = list(input_path.rglob('*.dxf')) + list(input_path.rglob('*.DXF'))

    if not dxf_files:
        raise ValueError("未找到DXF文件")

    # 创建输出目录
    if not output_dir:
        output_dir = input_path.parent / f"{input_path.stem}_parsed_comprehensive"
    output_dir = Path(output_dir)
    output_dir.mkdir(parents=True, exist_ok=True)

    logger.info(f"找到 {len(dxf_files)} 个DXF文件")
    logger.info(f"输出目录: {output_dir}")

    results = {"成功": 0, "失败": 0, "总计": len(dxf_files), "失败文件": []}

    for dxf_file in dxf_files:
        try:
            logger.info(f"开始处理: {dxf_file.name}")

            parser = DXFComprehensiveParser(str(dxf_file))
            result = parser.generate_output()

            if "错误" in result:
                results["失败"] += 1
                results["失败文件"].append(str(dxf_file))
                logger.error(f"解析失败: {dxf_file.name} - {result['错误']}")
                continue

            # 保存结果
            output_file = output_dir / f"{dxf_file.stem}.json"
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(result, f, ensure_ascii=False, indent=2)

            results["成功"] += 1
            logger.info(f"✓ 成功解析: {dxf_file.name}")

        except Exception as e:
            results["失败"] += 1
            results["失败文件"].append(str(dxf_file))
            logger.error(f"✗ 解析异常: {dxf_file.name} - {e}")

    logger.info(f"解析完成 - 成功: {results['成功']}, 失败: {results['失败']}")
    return results


if __name__ == '__main__':
    # 示例用法
    input_path = '/Users/<USER>/work/移动/项目-农商文旅/00-00 大数据/2025 市场项目/中广核/图纸格式转化_test/'  # 请修改为实际路径
    output_dir = './simple1_output'  # 输出到当前目录

    process_dxf_files(input_path, output_dir)
